package com.ximalaya.ting.android.host.manager.ad.videoad;

import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Paint;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.ximalaya.ting.android.ad.manager.VirtualClickReportManager;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.XmNativeAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.service.DownloadAdvertisParams;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Blur;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.NavigationUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.util.toast.ToastOption;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IThirdAdStatueCallBack;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ICollectStatusCallback;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdFreeListenProtocolDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivity;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivityClickRewardExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivityExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForDownload;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForFreeAd;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForFreeListenV2;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForGetVip;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForPoint;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForSingleTrack;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForWelfareCash;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForDownload;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForForwardVideo;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForFreeAd;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForFreeListenV2;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForGetVip;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForPoint;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForSingleTrack;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForSkits;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree3;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree4;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree6;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForWelfareCash;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.AdRewardCashTasksView;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.VideoCommentRecyclerView;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.VideoCommentViewAdapter;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadAdRecordManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.ad.AdActionBtnView;
import com.ximalaya.ting.android.host.view.ad.AdDownloadPermissionAndPrivacyDialog;
import com.ximalaya.ting.android.host.view.ad.AdSourceFromView;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.BusinessExtraInfo;
import com.ximalaya.ting.android.opensdk.model.advertis.VideoCommentInfo;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.player.advertis.followheart.ShakeUtilsNew;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import androidx.activity.OnBackPressedCallback;

/**
 * 全站畅听喜马广告渲染
 */
public class VideoAdFragment extends BaseFragment2 implements View.OnClickListener, IRewardAdFragment,
        VideoAdVideoPlayerManager.IVideoAdPlayerLister{
    private static final String TAG = "VideoAdFragment";

    public ViewGroup mVideoContainer;
    public RelativeLayout mVideoRootContainer;
    public ImageView mIvVideoState;
    public ImageView mBgIv;
    private XmLottieAnimationView mTipsAnimation;

    private boolean isNeedAddComment = false;
    private ViewGroup mCommentViewContainer;
    private VideoCommentRecyclerView mCommentRecyclerView;

    // 线索类 bottom 小卡片
    private View mLandStartLayout;
    private RoundImageView mLandStartCover;
    private TextView mLandStartTitle;
    private TextView mLandStartContent;
    private AdActionBtnView mLandStartBtn;
    private ImageView mLandStartClose;
    private ImageView mLandStartTag;

    // 线索类 bottom 大卡片
    private View mLandEndLayout;
    private RoundImageView mLandEndCover;
    private TextView mLandEndTitle;
    private TextView mLandEndContent;
    private AdActionBtnView mLandEndBtn;
    private ImageView mLandEndTag;


    // 下载类 bottom 小卡片
    private View mAppStartLayout;
    private RoundImageView mAppStartCover;
    private TextView mAppStartTitle;
    private TextView mAppStartContent;
    private AdActionBtnView mAppStartBtn;
    private ImageView mAppStartClose;
    private TextView mAppStartDeveloper;
    private TextView mAppStartVersion;
    private TextView mAppStartPermission;
    private TextView mAppStartPolicy;
    private ImageView mAppStartTag;

    // 下载类 bottom 大卡片
    private View mAppEndLayout;
    private RoundImageView mAppEndCover;
    private TextView mAppEndTitle;
    private TextView mAppEndContent;
    private AdActionBtnView mAppEndBtn;
    private TextView mAppEndDeveloper;
    private TextView mAppEndVersion;
    private TextView mAppEndPermission;
    private TextView mAppEndPolicy;
    private ImageView mAppEndTag;

    // 金币 底部大卡片
    private View mBottomNewLayout;
    private RoundImageView mNewCover;
    private TextView mNewTitle;
    private TextView mNewContent;
    private AdActionBtnView mNewBtn;
    private View mNewDownloadContainer;
    private TextView mNewDeveloper;
    private TextView mNewVersion;
    private TextView mNewPermission;
    private TextView mNewPolicy;
    private AdSourceFromView mNewAdSource;

    //video end
    private View mLayoutVideoEnd;
    private RoundImageView mIvVideoEnd;
    private TextView mTvVideoEndTitle;
    private TextView mTvVideoEndContent;
    private AdActionBtnView mTvVideoEndBtn;
    private TextView mTvVideoEndReplay;
    private ObjectAnimator mEndBtnAnimator;
    private ViewGroup mVideoEndDeveloperContainer;
    private TextView mVideoEndDeveloper;
    private TextView mVideoEndVersion;
    private ViewGroup mVideoEndPermissionContainer;
    private TextView mVideoEndPermission;
    private TextView mVideoEndPolicy;

    private AdSourceFromView mLandAdSource;

    private AdSourceFromView mAppAdSource;

    private Advertis mAdvertis;
    private AbstractRewardVideoAd<?> mAbstractThirdAd;
    private String mPositionName;
    private int mVideoPlayOverTime;
    private int mVideoCanCloseTime;
    private List<VideoCommentInfo> videoCommentInfoList = new ArrayList<>();
    private boolean commentClickEnable;

    public boolean isVisibleToUser = false;

    public VideoAdVideoPlayerManager mAdVideoPlayerManager;
    private long mRequestKey;
    private int mRewardCountDownStyle;
    private ICustomViewToActivity customViewByCountDownStyle;
    private View mTopCustomView;
    private CountDownTimer adMaxLoadCountDownTimer; // 广告加载超时检测
    private View.OnClickListener mClickListener;
    private RewardExtraParams mExtraParams;
    private boolean willFinish = false;
    private boolean isFirstResume = true;
    private boolean autoCloseWhenJumpBack;
    private boolean isAdClicked;

    private String clickButtonText;
    private String extraClickButtonText;
    private int extraClickTipsShowTime;
    private boolean hasShowExtraClickButton;

    private int tipsShowTime;

    private int defaultUiVisibility;
    private CountDownTimer jumpTimer; // 跳转计时器
    private boolean isJumping; // 点击后发生跳转
    private boolean isJumpCountdownFinish; //点击后发生跳转 倒计时是否结束
    private long mJumpMillis = 0;
    private boolean isDialogClickJumping;
    private boolean isClickDialogShowing;
    private boolean isVideoPlayComplete;

    // DP跳转优化相关变量
    private boolean isDpJumpOptimizationEnabled; // 是否启用DP跳转优化
    private long mPauseTime = 0; // onPause时的时间戳
    private long mResumeTime = 0; // onMyResume时的时间戳
    private long mTotalPauseTime = 0; // 累计暂停时间
    private boolean isSystemInterceptDialog = false; // 是否是系统拦截弹窗

    public static int systemUiVisibilityFlag = View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

    private long startLoadTime;

    private ShakeUtilsNew mShakeUtils;
    private boolean hasShakeSuccess = false;
    private boolean isShakeDetecting;
    private AdRewardCashTasksView mAdRewardCashTasksView;

    private Runnable showTipsRunnable = new Runnable() {
        @Override
        public void run() {
            if (mTipsAnimation != null) {
                mTipsAnimation.setVisibility(View.VISIBLE);
                mTipsAnimation.playAnimation();
            }
        }
    };

    public VideoAdFragment(RewardExtraParams extraParams, View.OnClickListener clickListener) {
        super(false, null);
        this.mClickListener = clickListener;
        this.mExtraParams = extraParams;
    }

    public static VideoAdFragment newInstance(long requestKey, RewardExtraParams extraParams, View.OnClickListener clickListener) {
        VideoAdFragment fragment = new VideoAdFragment(extraParams, clickListener);
        Bundle args = new Bundle();
        Advertis advertis = extraParams.getAdvertis();
        String positionName = extraParams.getPositionName();
        int videoPlayOverTime = extraParams.getVideoPlayOverTime();
        int canCloseTime = extraParams.getCanCloseTime();
        int rewardCountDownStyle = extraParams.getRewardCountDownStyle();
        args.putLong("requestKey", requestKey);
        args.putParcelable("ParamAdvertis", advertis);
        args.putString("ParamPosition", positionName);
        args.putInt("videoPlayOverTime", videoPlayOverTime);
        args.putInt("canCloseTime", canCloseTime);
        args.putInt("rewardCountDownStyle", rewardCountDownStyle);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle args = getArguments();
        if (getWindow() != null) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        defaultUiVisibility = getWindow().getDecorView().getSystemUiVisibility();
        if (args != null) {
            mRequestKey = args.getLong("requestKey");
            mAdvertis = args.getParcelable("ParamAdvertis");
            if (mAdvertis != null && mAdvertis instanceof AdUnLockVipTrackAdvertis) {
                videoCommentInfoList = ((AdUnLockVipTrackAdvertis) mAdvertis).getVideoCommentList();
                commentClickEnable = ((AdUnLockVipTrackAdvertis) mAdvertis).isCommentClickEnable();
            }
            mPositionName = args.getString("ParamPosition");
            mVideoPlayOverTime = args.getInt("videoPlayOverTime", 20);
            mRewardCountDownStyle = args.getInt("rewardCountDownStyle", RewardExtraParams.REWARD_COUNT_DOWN_STYLE);
            mVideoCanCloseTime = args.getInt("canCloseTime",10);
            if (mVideoCanCloseTime == 0) {
                mVideoCanCloseTime = 10;
            }
        }
        if (mExtraParams != null) {
            mAbstractThirdAd = mExtraParams.getAbstractThirdAd();
        }
        if (mAdvertis == null) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有数据"));
            finish();
            return;
        }

        //附加倒计时顶部布局
        if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3_AD_FIRST
                || mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForVipFree3();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_4){
            customViewByCountDownStyle = new RewardVideoCountDownStyleForVipFree4();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_5){
            customViewByCountDownStyle = new RewardVideoCountDownStyleForVipFree6();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_V2) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForFreeListenV2();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2) {
            customViewByCountDownStyle = new RewardVideoClickStyleForFreeListenV2();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForSingleTrack();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
            customViewByCountDownStyle = new RewardVideoClickStyleForSingleTrack();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SKITS_UNLOCK) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForSkits();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForForwardVideo();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForPoint();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT) {
            customViewByCountDownStyle = new RewardVideoClickStyleForPoint();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_VIP) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForGetVip();
        } else if (mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP) {
            customViewByCountDownStyle = new RewardVideoClickStyleForGetVip();
        } else if (mRewardCountDownStyle == REWARD_COUNT_DOWN_STYLE_FOR_WELFARE) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForWelfareCash();
        } else if (mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_WELFARE) {
            customViewByCountDownStyle = new RewardVideoClickStyleForWelfareCash();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForDownload();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD) {
            customViewByCountDownStyle = new RewardVideoClickStyleForDownload();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW) {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForFreeAd();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW) {
            customViewByCountDownStyle = new RewardVideoClickStyleForFreeAd();
        } else {
            customViewByCountDownStyle = new RewardVideoCountDownStyleForVipFree();
        }
        if (customViewByCountDownStyle instanceof ICustomViewToActivityClickRewardExt) {
            mTopCustomView = ((ICustomViewToActivityClickRewardExt)customViewByCountDownStyle).setCustomViewToActivity((ViewGroup) getView(),
                    mExtraParams, mClickListener, new ICustomViewToActivityClickRewardExt.ICountDownFinishCalLBack() {
                        @Override
                        public void onCountDownFinish() {
                            if (!isAdClicked) {
                                updateExtraClickButtonText(false);
                            }
                        }
                    });
        } else {
            mTopCustomView = customViewByCountDownStyle.setCustomViewToActivity((ViewGroup) getView(),
                    mExtraParams, mClickListener);
        }
        initConfig();
        initCashTaskView(mAdvertis, mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_WELFARE);
        // 静音处理
        View volume = customViewByCountDownStyle.getCustomVolumeView();
        if (volume != null && volume.getVisibility() == View.VISIBLE) {
            volume.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mAdVideoPlayerManager != null) {
                        mAdVideoPlayerManager.setVolume(volume.isSelected());
                        volume.setSelected(!volume.isSelected());
                    }
                }
            });
        }

        adMaxLoadCountDownTimer = null;
        int maxLoadTime = RewardExtraParams.getMaxLoadTime(mRewardCountDownStyle);
        Log.d("---RewardVideoAdManager", "VideoAdFragment initUi maxLoadTime =" + maxLoadTime);
        adMaxLoadCountDownTimer = new CountDownTimer(maxLoadTime, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                        callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME, "广告加载超时"));
                RewardVideoReport.reportXmLoadEnd(mAdvertis, System.currentTimeMillis() - startLoadTime, false, true);
                finish();
            }
        };
        adMaxLoadCountDownTimer.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(mRewardCountDownStyle));

        mBgIv = findViewById(R.id.host_video_ad_video_bg_iv);
        mVideoRootContainer = findViewById(R.id.host_video_ad_video_root_container);
        mVideoContainer = findViewById(R.id.host_video_ad_video_container);
        mIvVideoState = findViewById(R.id.host_video_ad_video_state);
        mTipsAnimation = findViewById(R.id.host_tips_animation);

        // 线索类广告
        mLandStartLayout = findViewById(R.id.host_video_ad_h5_layout_start);
        mLandStartCover = findViewById(R.id.host_video_ad_h5_layout_start_cover);
        mLandStartTitle = findViewById(R.id.host_video_ad_h5_layout_start_title);
        mLandStartContent = findViewById(R.id.host_video_ad_h5_layout_start_content);
        mLandStartBtn = findViewById(R.id.host_video_ad_h5_layout_start_btn);
        mLandStartTag = findViewById(R.id.host_video_ad_h5_layout_start_tag);
        mLandStartClose = findViewById(R.id.host_video_ad_h5_layout_start_close);

        mLandEndLayout = findViewById(R.id.host_video_ad_h5_layout_end);
        mLandEndCover = findViewById(R.id.host_video_ad_h5_layout_end_cover);
        mLandEndTitle = findViewById(R.id.host_video_ad_h5_layout_end_title);
        mLandEndContent = findViewById(R.id.host_video_ad_h5_layout_end_content);
        mLandEndBtn = findViewById(R.id.host_video_ad_h5_layout_end_btn);
        mLandEndTag = findViewById(R.id.host_video_ad_h5_layout_end_tag);
        mLandAdSource = findViewById(R.id.host_video_ad_h5_layout_start_source);


        // 下载类广告
        mAppStartLayout = findViewById(R.id.host_video_ad_app_layout_start);
        mAppStartCover = findViewById(R.id.host_video_ad_app_layout_start_cover);
        mAppStartTitle = findViewById(R.id.host_video_ad_app_layout_start_title);
        mAppStartContent = findViewById(R.id.host_video_ad_app_layout_start_content);
        mAppStartBtn = findViewById(R.id.host_video_ad_app_layout_start_btn);
        mAppStartClose = findViewById(R.id.host_video_ad_app_layout_start_close);
        mAppStartDeveloper = findViewById(R.id.host_video_ad_app_layout_start_developer);
        mAppStartVersion = findViewById(R.id.host_video_ad_app_layout_start_version);
        mAppStartPermission = findViewById(R.id.host_video_ad_app_layout_start_permission);
        mAppStartPermission.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        mAppStartPermission.getPaint().setAntiAlias(true);//抗锯齿
        mAppStartPolicy = findViewById(R.id.host_video_ad_app_layout_start_policy);
        mAppStartPolicy.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        mAppStartPolicy.getPaint().setAntiAlias(true);//抗锯齿
        mAppStartTag = findViewById(R.id.host_video_ad_app_layout_start_tag);
        mAppAdSource = findViewById(R.id.host_video_ad_app_layout_start_source);

        mAppEndLayout = findViewById(R.id.host_video_ad_app_layout_end);
        mAppEndCover = findViewById(R.id.host_video_ad_app_layout_end_cover);
        mAppEndTitle = findViewById(R.id.host_video_ad_app_layout_end_title);
        mAppEndContent = findViewById(R.id.host_video_ad_app_layout_end_content);
        mAppEndBtn = findViewById(R.id.host_video_ad_app_layout_end_btn);
        mAppEndDeveloper = findViewById(R.id.host_video_ad_app_layout_end_developer);
        mAppEndVersion = findViewById(R.id.host_video_ad_app_layout_end_version);
        mAppEndPermission = findViewById(R.id.host_video_ad_app_layout_end_permission);
        mAppEndPolicy = findViewById(R.id.host_video_ad_app_layout_end_policy);
        mAppEndTag = findViewById(R.id.host_video_ad_app_layout_end_tag);

        // 评论弹幕
        mCommentViewContainer = findViewById(R.id.host_video_comment_container);
        mCommentRecyclerView = findViewById(R.id.host_video_comment_recycler);

        // 金币广告位 新的底部卡片
        mBottomNewLayout = findViewById(R.id.host_video_ad_bottom_layout_new);
        mNewCover = findViewById(R.id.host_video_ad_new_cover);
        mNewTitle = findViewById(R.id.host_video_ad_new_title);
        mNewContent = findViewById(R.id.host_video_ad_new_content);
        mNewBtn = findViewById(R.id.host_video_ad_new_btn);
        mNewDownloadContainer = findViewById(R.id.host_video_ad_new_download_container);
        mNewDeveloper = findViewById(R.id.host_video_ad_new_developer);
        mNewVersion = findViewById(R.id.host_video_ad_new_version);
        mNewPermission = findViewById(R.id.host_video_ad_new_permission);
        mNewPermission.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        mNewPermission.getPaint().setAntiAlias(true);//抗锯齿
        mNewPolicy = findViewById(R.id.host_video_ad_new_policy);
        mNewPolicy.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        mNewPolicy.getPaint().setAntiAlias(true);//抗锯齿
        mNewAdSource = findViewById(R.id.host_video_ad_new_source);

        // 完播定帧
        mLayoutVideoEnd = findViewById(R.id.host_video_ad_video_end_layout);
        mIvVideoEnd = findViewById(R.id.host_video_ad_video_end_iv);
        mTvVideoEndTitle = findViewById(R.id.host_video_ad_video_end_title);
        mTvVideoEndContent = findViewById(R.id.host_video_ad_video_end_content);
        mTvVideoEndBtn = findViewById(R.id.host_video_ad_video_end_btn);
        mTvVideoEndReplay = findViewById(R.id.host_video_ad_video_end_replay);
        mVideoEndDeveloperContainer = findViewById(R.id.host_video_ad_video_end_developer_container);
        mVideoEndPermissionContainer = findViewById(R.id.host_video_ad_video_end_permission_container);
        mVideoEndDeveloper = findViewById(R.id.host_video_ad_video_end_developer);
        mVideoEndVersion = findViewById(R.id.host_video_ad_video_end_version);
        mVideoEndPermission = findViewById(R.id.host_video_ad_video_end_permission);
        mVideoEndPermission.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        mVideoEndPermission.getPaint().setAntiAlias(true);
        mVideoEndPolicy = findViewById(R.id.host_video_ad_video_end_policy);
        mVideoEndPolicy.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        mVideoEndPolicy.getPaint().setAntiAlias(true);
        mIvVideoEnd.setUseCache(false);
        // 如果三方物料不为空，走三方曝光回调
        if (mAbstractThirdAd == null) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callback -> callback.onAdLoad(XmNativeAd.createXmNativeAdByAdvertis(mAdvertis)));
        }
        startLoadTime = System.currentTimeMillis();
        RewardVideoReport.reportXmLoadStart(mAdvertis, startLoadTime);
        initView();
    }

    private void initCashTaskView(Advertis advertis, boolean isClickRewardType) {
        if (advertis == null || AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL.equals(mPositionName)) {
            return;
        }
        if (mRewardCountDownStyle == REWARD_COUNT_DOWN_STYLE_FOR_WELFARE || mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_WELFARE) {
            mAdRewardCashTasksView = findViewById(R.id.reward_cash_task_view);
            mAdRewardCashTasksView.setVisibility(View.VISIBLE);
            mAdRewardCashTasksView.setCustomViewToActivity((ViewGroup) getView(), mExtraParams, mClickListener, () -> {
                if (!isAdClicked) {
                    updateExtraClickButtonText(false);
                }
            });
            mAdRewardCashTasksView.setData(advertis, isClickRewardType);
            RelativeLayout rlClickLayout  = mAdRewardCashTasksView.findViewById(R.id.rl_click_layout);
            if (rlClickLayout != null && isClickRewardType) {
                rlClickLayout.setOnClickListener(this);
            }
        }
    }

    private void initView() {
        mEndBtnAnimator = ObjectAnimator.ofFloat(mTvVideoEndBtn, "rotation", 0,
                -10, 10, -10, 10, -8, 8, 0, 0, 0, 0);
        mEndBtnAnimator.setDuration(1000);
        mEndBtnAnimator.setRepeatCount(1);
        mEndBtnAnimator.setStartDelay(500);
        if (useNewBottomLayout()) {
            initForNewBottom();
        } else {
            if (mAbstractThirdAd == null && AdManager.isDownloadAd(mAdvertis)) {
                initForAppAd();
            } else {
                initForLandAd();
            }
        }
        initCommentView();
        initClickListener();
        mAdVideoPlayerManager = new VideoAdVideoPlayerManager(this,
                mAbstractThirdAd != null ? mAbstractThirdAd.getDynamicUrl() : mAdvertis.getVideoCover());
        mAdVideoPlayerManager.setVolume(true);
        updateButtonText(false);
    }

    private void initClickListener() {
        mLandStartClose.setOnClickListener(this);
        mAppStartClose.setOnClickListener(this);
        mLandStartLayout.setOnClickListener(this);
        mLandEndLayout.setOnClickListener(this);
        mAppStartLayout.setOnClickListener(this);
        mAppEndLayout.setOnClickListener(this);
        mBottomNewLayout.setOnClickListener(this);
        mTvVideoEndBtn.setOnClickListener(this);

        List<View> clickViews = new ArrayList<>();
        clickViews.add(mLandStartLayout);
        clickViews.add(mLandEndLayout);
        clickViews.add(mAppStartLayout);
        clickViews.add(mAppEndLayout);
        clickViews.add(mBottomNewLayout);
        clickViews.add(mTvVideoEndBtn);
        if (AdUnlockUtil.isFullScreenClick(mExtraParams)) {
            mBgIv.setOnClickListener(this);
            clickViews.add(mBgIv);
        } else {
            mVideoContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    NavigationUtil.hideNavigationBar(getWindow(), true);
                }
            });
            boolean isNeedFullClick = ConfigureCenter.getInstance()
                    .getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_HORIZONTAL_AD_FULL_CLICK, false);
            if (AdUnlockUtil.isHorizontalVideoAd(mAdvertis) && isNeedFullClick) {
                // 横屏广告背景设置点击
                mBgIv.setOnClickListener(this);
                clickViews.add(mBgIv);
            }
        }
        mTvVideoEndReplay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mAdVideoPlayerManager != null) {
                    mAdVideoPlayerManager.replay();
                }
                resetViewForReplay();
            }
        });

        if (mAbstractThirdAd != null){
            mAbstractThirdAd.bindInteractionListener(mVideoRootContainer, clickViews, new IThirdAdStatueCallBack() {
                @Override
                public void onADExposed() {
                    RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                            callback->callback.onAdLoad(mAbstractThirdAd));
                }

                @Override
                public void onADClicked() {
                    doClick(1);
                }

                @Override
                public void onADError(int code, String msg) {

                }

                @Override
                public void onADStatusChanged() {

                }
            });
        }

        mAppStartPermission.setOnClickListener(permissionListener);
        mAppStartPolicy.setOnClickListener(privacyListener);
        mAppEndPermission.setOnClickListener(permissionListener);
        mAppEndPolicy.setOnClickListener(privacyListener);
        mNewPermission.setOnClickListener(permissionListener);
        mNewPolicy.setOnClickListener(privacyListener);
        mVideoEndPermission.setOnClickListener(permissionListener);
        mVideoEndPolicy.setOnClickListener(privacyListener);
    }

    private void initCommentViewClick() {
        mCommentRecyclerView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    View childView = mCommentRecyclerView.findChildViewUnder(event.getX(), event.getY());
                    if (childView == null) {
                        // 点击了顶部空白区域
                        doClick(1);
                        return true;
                    }
                }
                return false;
            }
        });
    }

    private void initForLandAd() {
        mLandStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        mLandEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        mTvVideoEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD_END);
        if (mAbstractThirdAd != null){
            ImageManager.from(getContext()).displayImage(mLandStartCover, mAbstractThirdAd.getAdIcon(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            ImageManager.from(getContext()).displayImage(mLandEndCover,mAbstractThirdAd.getAdIcon(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            mLandStartTag.setImageResource(R.drawable.host_ad_tag_style_baidu_1);
            mLandEndTag.setImageResource(R.drawable.host_ad_tag_style_baidu_1);
            mLandStartTitle.setText(mAbstractThirdAd.getTitle());
            mLandEndTitle.setText(mAbstractThirdAd.getTitle());
            mLandStartContent.setText(mAbstractThirdAd.getDesc());
            mLandEndContent.setText(mAbstractThirdAd.getDesc());
            if (!TextUtils.isEmpty(mAbstractThirdAd.getButtonText())) {
                mLandStartBtn.setText(mAbstractThirdAd.getButtonText());
                mLandEndBtn.setText(mAbstractThirdAd.getButtonText());
                mTvVideoEndBtn.setText(mAbstractThirdAd.getButtonText());
            }
        } else {
            ImageManager.from(getContext()).displayImage(mLandStartCover, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            ImageManager.from(getContext()).displayImage(mLandEndCover, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            mLandStartTitle.setText(mAdvertis.getName());
            mLandEndTitle.setText(mAdvertis.getName());
            mLandStartContent.setText(mAdvertis.getDescription());
            mLandEndContent.setText(mAdvertis.getDescription());
            if (mLandAdSource != null) {
                mLandAdSource.setAdvertis(mAdvertis);
            }
        }
        mLandStartLayout.setAlpha(0f);
        mLandStartLayout.setVisibility(View.VISIBLE);
        if (AdUnlockUtil.isHorizontalVideoAd(mAdvertis)) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mVideoRootContainer.getLayoutParams();
            lp.addRule(RelativeLayout.ABOVE, R.id.host_video_ad_h5_layout_start);
            mVideoRootContainer.setLayoutParams(lp);

            if (mVideoContainer != null) {
                adapterHorVideoContainer();
            }
        }
        RelativeLayout.LayoutParams tipsLp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
        tipsLp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 28));
        mTipsAnimation.setLayoutParams(tipsLp);
    }

    private void initForAppAd() {
        ImageManager.from(getContext()).displayImage(mAppStartCover, mAdvertis.getDownloadAppLogo(), -1,
                BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
        ImageManager.from(getContext()).displayImage(mAppEndCover, mAdvertis.getDownloadAppLogo(), -1,
                BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
        mAppStartTitle.setText(mAdvertis.getDownloadAppName());
        mAppEndTitle.setText(mAdvertis.getDownloadAppName());
        mAppStartContent.setText(mAdvertis.getDownloadAppDesc());
        mAppEndContent.setText(mAdvertis.getDownloadAppDesc());
        mAppStartBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        mAppEndBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        mVideoEndPermissionContainer.setVisibility(View.VISIBLE);
        mVideoEndDeveloperContainer.setVisibility(View.VISIBLE);
        mTvVideoEndBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD_END);
        if (TextUtils.isEmpty(mAdvertis.getAppDeveloper())) {
            mAppStartDeveloper.setText("开发者: 未知");
            mAppEndDeveloper.setText("开发者: 未知");
            mVideoEndDeveloper.setText("开发者: 未知");
        } else {
            mAppStartDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
            mAppEndDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
            mVideoEndDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
        }
        if (TextUtils.isEmpty(mAdvertis.getAppVersion())) {
            mAppStartVersion.setText("版本号: 未知");
            mAppEndVersion.setText("版本号: 未知");
            mVideoEndVersion.setText("版本号: 未知");
        } else {
            mAppStartVersion.setText("版本号:" + mAdvertis.getAppVersion());
            mAppEndVersion.setText("版本号:" + mAdvertis.getAppVersion());
            mVideoEndVersion.setText("版本号:" + mAdvertis.getAppVersion());
        }
        mAppStartLayout.setAlpha(0f);
        mAppStartLayout.setVisibility(View.VISIBLE);
        if (AdUnlockUtil.isHorizontalVideoAd(mAdvertis)) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mVideoRootContainer.getLayoutParams();
            lp.addRule(RelativeLayout.ABOVE, R.id.host_video_ad_app_layout_start);
            mVideoRootContainer.setLayoutParams(lp);
            if (mVideoContainer != null) {
                adapterHorVideoContainer();
            }
        }
        RelativeLayout.LayoutParams tipsLp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
        tipsLp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 70));
        mTipsAnimation.setLayoutParams(tipsLp);
        if (mAppAdSource != null) {
            mAppAdSource.setAdvertis(mAdvertis);
        }
    }

    private void initCommentView() {
        if (ToolUtil.isEmptyCollects(videoCommentInfoList)) {
            mCommentViewContainer.setVisibility(View.GONE);
            return;
        }
        isNeedAddComment = true;
        mCommentViewContainer.setVisibility(View.VISIBLE);
        View.OnClickListener commentClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doClick(3);
            }
        };
        VideoCommentViewAdapter adapter = new VideoCommentViewAdapter(commentClickEnable ? commentClickListener : null);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        linearLayoutManager.setStackFromEnd(true);
        mCommentRecyclerView.setLayoutManager(linearLayoutManager);
        mCommentRecyclerView.setAdapter(adapter);
        mCommentRecyclerView.setData(videoCommentInfoList, adapter, mExtraParams);
    }

    private void initForNewBottom() {
        mNewBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        RelativeLayout.LayoutParams tipsLp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
        if (AdManager.isDownloadAd(mAdvertis)) {
            ImageManager.from(getContext()).displayImage(mNewCover, mAdvertis.getDownloadAppLogo(), -1,
                    BaseUtil.dp2px(getContext(), 48), BaseUtil.dp2px(getContext(), 48));
            mNewTitle.setText(mAdvertis.getDownloadAppName());
            mNewContent.setText(mAdvertis.getDownloadAppDesc());
            tipsLp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 0), BaseUtil.dp2px(getContext(), 30));
            mNewDownloadContainer.setVisibility(View.VISIBLE);
            mVideoEndPermissionContainer.setVisibility(View.VISIBLE);
            mVideoEndDeveloperContainer.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(mAdvertis.getAppDeveloper())) {
                mNewDeveloper.setText("开发者: 未知");
                mVideoEndDeveloper.setText("开发者: 未知");
            } else {
                mNewDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
                mVideoEndDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
            }
            if (TextUtils.isEmpty(mAdvertis.getAppVersion())) {
                mNewVersion.setText("版本号: 未知");
                mVideoEndVersion.setText("版本号: 未知");
            } else {
                mNewVersion.setText("版本号:" + mAdvertis.getAppVersion());
                mVideoEndVersion.setText("版本号:" + mAdvertis.getAppVersion());
            }
        } else {
            ImageManager.from(getContext()).displayImage(mNewCover, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            mNewTitle.setText(mAdvertis.getName());
            mNewContent.setText(mAdvertis.getDescription());
            tipsLp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 0), BaseUtil.dp2px(getContext(), 5));
        }
        mTipsAnimation.setLayoutParams(tipsLp);
        mTvVideoEndBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD_END);
        mBottomNewLayout.setVisibility(View.VISIBLE);

        if (mNewAdSource != null) {
            mNewAdSource.setAdvertis(mAdvertis);
        }

        if (AdUnlockUtil.isHorizontalVideoAd(mAdvertis)) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mVideoRootContainer.getLayoutParams();
            lp.addRule(RelativeLayout.ABOVE, R.id.host_video_ad_bottom_layout_new);
            mVideoRootContainer.setLayoutParams(lp);
            if (mVideoContainer != null) {
                adapterHorVideoContainer();
            }

            lp = (RelativeLayout.LayoutParams) mCommentViewContainer.getLayoutParams();
            lp.addRule(RelativeLayout.BELOW, R.id.host_video_ad_video_container);
            lp.height = ViewGroup.LayoutParams.MATCH_PARENT;
            if (BaseUtil.isSmallScreenDevice(getContext())) {
                lp.setMargins(BaseUtil.dp2px(getContext(), 16), BaseUtil.dp2px(getContext(), 24),
                        BaseUtil.dp2px(getContext(), 44), BaseUtil.dp2px(getContext(), 16));
            } else {
                lp.setMargins(BaseUtil.dp2px(getContext(), 16), BaseUtil.dp2px(getContext(), 32),
                        BaseUtil.dp2px(getContext(), 44), BaseUtil.dp2px(getContext(), 16));
            }
            mCommentViewContainer.setLayoutParams(lp);
        } else {
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    int bottomMargin = mBottomNewLayout.getMeasuredHeight() + BaseUtil.dp2px(getContext(), 16);
                    RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mCommentViewContainer.getLayoutParams();
                    lp.setMargins(BaseUtil.dp2px(getContext(), 16), 0, BaseUtil.dp2px(getContext(), 44), bottomMargin);
                    mCommentViewContainer.setLayoutParams(lp);
                }
            });

        }
    }

    public void adapterHorVideoContainer() {
        if (getHorContainerDisplayedEnable()) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mVideoContainer.getLayoutParams();
            lp.height = BaseUtil.getScreenWidth(getContext()) * 9 / 16;
            mVideoContainer.setLayoutParams(lp);
        }
    }

    private boolean useNewBottomLayout() {
        // 有评论弹幕时显示新布局
        if (!ToolUtil.isEmptyCollects(videoCommentInfoList)) {
            return true;
        }
        return false;
    }

    private void resetViewForReplay() {
        if (useNewBottomLayout()) {
            mBottomNewLayout.setVisibility(View.VISIBLE);
        } else {
            if (AdManager.isDownloadAd(mAdvertis)) {
                mAppStartLayout.setAlpha(0f);
                mAppStartLayout.setVisibility(View.VISIBLE);
            } else {
                mLandStartLayout.setAlpha(0f);
                mLandStartLayout.setVisibility(View.VISIBLE);
            }
        }
        mLayoutVideoEnd.setVisibility(View.GONE);
        startCountDown();
        startShakeDetect();
    }

    private View.OnClickListener permissionListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (mAdvertis == null
                    || ((mAdvertis.getAppPermissions() == null || mAdvertis.getAppPermissions().isEmpty())
                    && (mAdvertis.getBusinessExtraInfo() == null || TextUtils.isEmpty(mAdvertis.getBusinessExtraInfo().getAppPermissionUrl())))) {
                return;
            }
            AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), mAdvertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PERMISSION);
            dialog.show();
            String positionName = AdPositionIdManager.getPositionNameByPositionId(mAdvertis.getAdPositionId());
            DownloadAdvertisParams params = new DownloadAdvertisParams(mAdvertis, positionName);
            DownloadServiceManage.getInstance().recordDownloadDialogOkClick(mAdvertis.getRealLink(),
                    DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PERMISSION, params);
        }
    };

    private View.OnClickListener privacyListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (mAdvertis == null || TextUtils.isEmpty(mAdvertis.getAppPrivacyPolicy())) {
                return;
            }
            AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), mAdvertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PRIVACY);
            dialog.show();
            String positionName = AdPositionIdManager.getPositionNameByPositionId(mAdvertis.getAdPositionId());
            DownloadAdvertisParams params = new DownloadAdvertisParams(mAdvertis, positionName);
            DownloadServiceManage.getInstance().recordDownloadDialogOkClick(mAdvertis.getRealLink(),
                    DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PRIVACY, params);
        }
    };

    @SuppressLint("StaticFieldLeak")
    public void doBlurTask(Bitmap originBitmap) {
        // 横版广告不进行背景渲染
        if (AdUnlockUtil.isHorizontalVideoAd(mAdvertis)) {
            return;
        }
        Context context = getContext();
        new MyAsyncTask<Void, Void, Bitmap>() {
            @Override
            protected Bitmap doInBackground(Void... voids) {
                return Blur.fastBlur(context, originBitmap, 10);
            }

            @Override
            protected void onPostExecute(Bitmap bitmap) {
                Log.d("wupei", "doBlurTask onPostExecute bitmap == null?" + (bitmap == null));
                if (!canUpdateUi()) {
                    return;
                }
                if (bitmap != null) {
                    mBgIv.setBackground(null);
                    mBgIv.setImageBitmap(bitmap);
                }
            }
        }.execute();
    }
    @Override
    public void onProgress(long curPosition, long duration) {
        if (customViewByCountDownStyle != null) {
            customViewByCountDownStyle.onAdPlayProgress(curPosition, duration);
        }
        if (mAdRewardCashTasksView != null) {
            mAdRewardCashTasksView.onAdPlayProgress(curPosition, duration);
        }
        if (mAdvertis != null && getVideoContainer() != null) {
            VirtualClickReportManager.getInstance().reportExposureByDuration(getContext(), mAdvertis, (int) curPosition, (int) duration, getVideoContainer());
        }
    }

    public void showVideoCompleteView() {
        isVideoPlayComplete = true;
        if (mAdvertis == null) {
            return;
        }
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayComplete);
        if (mExtraParams != null && mExtraParams.getCountDownTimer() != null) {
            mExtraParams.getCountDownTimer().cancel();
            mExtraParams.setCanCloseTime(RewardVideoCountDownStyleForVipFree.LAST_VIDEO_PLAY_FINISH);
        }
        if (customViewByCountDownStyle != null) {
            customViewByCountDownStyle.onAdPlayComplete();
        }
        if (useNewBottomLayout()) {
            mBottomNewLayout.setVisibility(View.INVISIBLE);
        } else {
            mLandStartLayout.setVisibility(View.INVISIBLE);
            mLandEndLayout.setVisibility(View.GONE);
            mAppStartLayout.setVisibility(View.INVISIBLE);
            mAppEndLayout.setVisibility(View.GONE);
        }

        mLayoutVideoEnd.setVisibility(View.VISIBLE);

        if (mAbstractThirdAd != null){
            mTvVideoEndTitle.setText(mAbstractThirdAd.getTitle());
            mTvVideoEndContent.setText(mAbstractThirdAd.getDesc());
            ImageManager.from(getContext()).displayImage(mIvVideoEnd, mAbstractThirdAd.getAdIcon(), -1,
                    BaseUtil.dp2px(getContext(), 90), BaseUtil.dp2px(getContext(), 90));
        } else {
            mTvVideoEndTitle.setText(mAdvertis.getName());
            mTvVideoEndContent.setText(mAdvertis.getDescription());
            ImageManager.from(getContext()).displayImage(mIvVideoEnd, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 90), BaseUtil.dp2px(getContext(), 90));
        }
        if (mAdvertis.isVideoAutoJump()){
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback -> callback.onAdVideoClick(true, 1));
            if (mExtraParams != null) {
                XmBaseDialog closeDialog = mExtraParams.getVipFreeCloseAlertDialog();
                if (closeDialog != null && closeDialog.isShowing()) {
                    closeDialog.dismiss();
                }
            }
            autoCloseWhenJumpBack = true;
            if (customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
                ((ICustomViewToActivityExt) customViewByCountDownStyle).videoCompleteAutoJump();
            }
        } else {
            // 执行摇动动画
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    mTvVideoEndBtn.setPivotX(mTvVideoEndBtn.getWidth() /2);
                    mTvVideoEndBtn.setPivotY(mTvVideoEndBtn.getHeight() /2);
                    mEndBtnAnimator.start();
                }
            }, 500);
        }
        stopShakeDetect();
        mCommentRecyclerView.release();
    }


    @Override
    public int getContainerLayoutId() {
        return R.layout.host_frame_video_ad_new;
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d("wupei11", "onPause");
        VideoAdTrace.traceEvent(mAdvertis, "onPause");
        isVisibleToUser = false;

        // DP跳转优化：记录暂停时间
        if (isDpJumpOptimizationEnabled && isJumping && !isJumpCountdownFinish) {
            mPauseTime = System.currentTimeMillis();
            Logger.i("JumpCountdown", "DP跳转优化 - 记录暂停时间: " + mPauseTime);
        }

        if (mAdVideoPlayerManager != null) {
            mAdVideoPlayerManager.onPauseMy();
        }
        if (mExtraParams != null && mExtraParams.getCountDownTimer() != null) {
            mExtraParams.getCountDownTimer().pause();
        }
        if (mAdRewardCashTasksView != null && mAdRewardCashTasksView.getCountDownTimer() != null) {
            mAdRewardCashTasksView.getCountDownTimer().pause();
        }
        mCommentRecyclerView.onPagePause();
        ViewUtil.keepScreenOn(getActivity(), false);
        NavigationUtil.hideNavigationBar(getWindow(), false);
        stopShakeDetect();
        if (getWindow() != null) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    public void onStop() {
        super.onStop();
        Log.d("wupei11", "onStop");
        VideoAdTrace.traceEvent(mAdvertis, "onStop");
    }

    public void onStart() {
        super.onStart();
        Log.d("wupei11", "onStart");
        VideoAdTrace.traceEvent(mAdvertis, "onStart");
    }

    private void startJumpCountdown(int remainStayTimeSeconds) {
        if (jumpTimer != null) return;
        mJumpMillis = System.currentTimeMillis();
        isJumpCountdownFinish = false;

        // 检查是否需要启用DP跳转优化
        isDpJumpOptimizationEnabled = mAdvertis != null && !TextUtils.isEmpty(mAdvertis.getDpRealLink());
        if (isDpJumpOptimizationEnabled) {
            mTotalPauseTime = 0; // 重置累计暂停时间
            Logger.i("JumpCountdown", "DP跳转优化已启用");
        }

        jumpTimer = new CountDownTimer((long)remainStayTimeSeconds * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                Logger.i("JumpCountdown", "onTick = " + millisUntilFinished);
            }

            @Override
            public void onFinish() {
                Logger.i("JumpCountdown", "finish");
                VideoAdTrace.traceEvent(mAdvertis, "jumpCountDownFinish");
                isJumpCountdownFinish = true;
                updateButtonText(true);
                if (customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
                    ((ICustomViewToActivityExt) customViewByCountDownStyle).onAdCanReward();
                }
                if (hasShowExtraClickButton) {
                    updateExtraClickButtonText(true);
                }
                if (mAdRewardCashTasksView != null) {
                    mAdRewardCashTasksView.onAdCanReward();
                }
            }
        };
        jumpTimer.start();
    }

    private void cancelJumpCountdown() {
        if (jumpTimer != null) {
            jumpTimer.cancel();
            jumpTimer = null;
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        Log.d("wupei11", "onResume");
        isVisibleToUser = true;
        ViewUtil.keepScreenOn(getActivity(), true);

        // DP跳转优化：处理系统拦截弹窗的情况
        if (isDpJumpOptimizationEnabled && isJumping && !isJumpCountdownFinish && mPauseTime > 0) {
            mResumeTime = System.currentTimeMillis();
            long pauseDuration = mResumeTime - mPauseTime;

            // 判断是否为系统拦截弹窗（暂停时间较短，通常小于3秒）
            if (pauseDuration < 3000) {
                isSystemInterceptDialog = true;
                mTotalPauseTime += pauseDuration;
                Logger.i("JumpCountdown", "DP跳转优化 - 检测到系统拦截弹窗，暂停时长: " + pauseDuration + "ms，累计暂停时长: " + mTotalPauseTime + "ms");
            } else {
                isSystemInterceptDialog = false;
                Logger.i("JumpCountdown", "DP跳转优化 - 正常跳转返回，暂停时长: " + pauseDuration + "ms");
            }
            mPauseTime = 0; // 重置暂停时间
        }

        if (AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION.equals(mPositionName)
                && !MmkvCommonUtil.getInstance(getContext()).getBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, false)
                && !isFromPlayPageRewardVip()) {
            // 时长模式展示规则弹窗
            AdFreeListenProtocolDialog protocolDialog = new AdFreeListenProtocolDialog(BaseApplication.getMainActivity(), new AdFreeListenProtocolDialog.IDismissCallBack() {
                @Override
                public void onDismiss() {
                    NavigationUtil.hideNavigationBar(getWindow(), true);
                }
            });
            protocolDialog.show();
            MmkvCommonUtil.getInstance(getContext()).saveBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, true);
        } else {
            NavigationUtil.hideNavigationBar(getWindow(), true);
        }
        VideoAdTrace.traceEvent(mAdvertis, "onMyResume");
        if (mAdVideoPlayerManager != null && (!isDialogClickJumping || isJumpCountdownFinish) && !isClickDialogShowing) {
            mAdVideoPlayerManager.onResumeMy();
        }
        if (mExtraParams != null){
            if(mExtraParams.getCountDownTimer() != null && (!isDialogClickJumping || isJumpCountdownFinish) && !isClickDialogShowing) {
                mExtraParams.getCountDownTimer().resume();
            }
            if (mExtraParams.getRewardPageStatusCallBack() != null){
                mExtraParams.getRewardPageStatusCallBack().onPageResume(getActivity(),
                        RewardExtraParams.IRewardPageStatusCallBack.SOURCE_FROM_AD_PAGE);
            }
        }
        if (mAdRewardCashTasksView != null && mAdRewardCashTasksView.getCountDownTimer() != null
                && (!isDialogClickJumping || isJumpCountdownFinish) && !isClickDialogShowing) {
            mAdRewardCashTasksView.getCountDownTimer().resume();
        }

        // 站内iting的dp 会被FixLaunchModeActivity拦截，所以会多执行一次onResume
        if (isJumping && !AdUnlockUtil.isFixLaunchModeActivityShowed()) {
            VideoAdTrace.traceEvent(mAdvertis, "onResume_CancelJumpCount");
            checkJumpWithOptimization();
        }
        if (isFirstResume) {
            isFirstResume = false;
            return;
        }
        mCommentRecyclerView.onPageResume();
        if (autoCloseWhenJumpBack && customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
            ((ICustomViewToActivityExt) customViewByCountDownStyle).closeAdWhenAutoJumpBack();
        }
        startShakeDetect();
        if (getWindow() != null) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }


    public boolean isFromPlayPageRewardVip() {
        //时长模式下，播放页免广告弹窗来源不展示规则弹窗，因为其公用的是时长模式的广告位，但本身不是时长模式的奖励而是免广时间的奖励
        return mExtraParams != null && (mExtraParams.getRewardCountDownStyle() == REWARD_CLICK_STYLE_FOR_FREE_AD_NEW || mExtraParams.getRewardCountDownStyle() == REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW);
    }
    private boolean isRecallDp(){
        if (mAdvertis == null) return false;
        return !TextUtils.isEmpty(mAdvertis.getDpRealLink()) &&
                mAdvertis.getDpRetrySecond() > 0 &&
                System.currentTimeMillis() - mJumpMillis < mAdvertis.getDpRetrySecond() * 1000L;
    }

    private void checkJump() {
        if (!isJumpCountdownFinish && isDialogClickJumping && mAdvertis != null) {
            int stayTimeSeconds = (int) ((System.currentTimeMillis() - mJumpMillis ) / 1000); // 已经停留的时长
            mAdvertis.setActualStayTime(mAdvertis.getActualStayTime() - stayTimeSeconds);
            mAdvertis.setTipStayTime(mAdvertis.getTipStayTime() - stayTimeSeconds);
            showClickDialog(mAdvertis.getBusinessExtraInfo().getParsedIncentivePopupInfo(),
                    mAdvertis.getTipStayTime(), mAdvertis.getActualStayTime(), true);
        }
        // 这里有可能会触发DP二跳
        if (!isRecallDp()) {
            isJumping = false;
            isDialogClickJumping = false;
        }
        if (!isJumpCountdownFinish && mAdRewardCashTasksView != null && mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_WELFARE) {
            isJumpCountdownFinish = true;
            CustomToast.showSuccessToast("浏览未满足时长要求，请重试");
        }
        cancelJumpCountdown();
    }

    @Override
    protected String getPageLogicName() {
        return "rewardVideoAd";
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelShowTipsAnimation();
        stopShakeDetect();
        mShakeUtils = null;
        NavigationUtil.hideNavigationBar(getWindow(), false);
        mCommentRecyclerView.release();
        if (getWindow() != null) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    @Override
    public void finish() {
        willFinish = true;

        if (mVideoContainer != null) {
            mVideoContainer.removeAllViews();
        }
        if (mAdVideoPlayerManager != null) {
            mAdVideoPlayerManager.release();
        }
        if (customViewByCountDownStyle != null) {
            View volume = customViewByCountDownStyle.getCustomVolumeView();
            if (volume != null) {
                volume.setOnClickListener(null);
            }
        }
        if (mAdRewardCashTasksView != null) {
            mAdRewardCashTasksView.cancelCountdown();
        }
        super.finish();
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    //播放失败的回调
    public void onPlayError() {
        if (adMaxLoadCountDownTimer != null) {
            adMaxLoadCountDownTimer.cancel();
        }
        if (!canUpdateUi()) {
            return;
        }
        AnimationUtil.stopAnimation(mIvVideoState);
        mIvVideoState.setVisibility(View.INVISIBLE);
        RewardVideoReport.reportXmLoadEnd(mAdvertis, System.currentTimeMillis() - startLoadTime, false, false);
        finish();
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                callback->callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "播放出错"));
    }

    public void onPlayStart() {
        if (isNeedAddComment) {
            mCommentRecyclerView.startAddCommentTask();
            isNeedAddComment = false;
        }
        if (adMaxLoadCountDownTimer != null) {
            adMaxLoadCountDownTimer.cancel();
        }
        if (customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
            ((ICustomViewToActivityExt) customViewByCountDownStyle).onAdPlayStart();
        }
    }

    public void startCountDown() {
        if (!isVideoPlayComplete && mAdvertis != null && mAdvertis.getBusinessExtraInfo() != null
                && mExtraParams != null && mExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_DURATION) {
            BusinessExtraInfo.IncentivePopupInfo incentivePopupInfo = mAdvertis.getBusinessExtraInfo().getParsedIncentivePopupInfo();
            if (incentivePopupInfo != null) {
                initForClickDialog(incentivePopupInfo);
            }
        }
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayStart);
        if (useNewBottomLayout()) {
            return;
        }
        View animationView = AdManager.isDownloadAd(mAdvertis) ? mAppStartLayout : mLandStartLayout;
        RewardVideoReport.reportXmLoadEnd(mAdvertis, System.currentTimeMillis() - startLoadTime, true, false);
        HandlerManager.postOnUIThread(() -> {
            ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(animationView,
                    "alpha", 0f, 1.0f);
            ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(animationView,
                    "translationY", animationView.getHeight(), 0);
            alphaAnimator.setDuration(500);
            translationYAnimator.setDuration(500);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(alphaAnimator, translationYAnimator);
            animatorSet.start();
        });
    }

    @Override
    public View getVideoStateView() {
        return mIvVideoState;
    }

    @Override
    public ViewGroup getVideoContainer() {
        return mVideoContainer;
    }

    @Override
    public void onTimerPause() {

    }

    @Override
    public void onTimerResume() {

    }

    @Override
    public boolean isVisibleToUser() {
        return isVisibleToUser;
    }

    @Override
    public boolean onBackPressed() {
        if (willFinish) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public void onClick(View v) {
        if (!canUpdateUi() || v == null) {
            return;
        }
        if (v.getVisibility() != View.VISIBLE) {
            return;
        }
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.host_video_ad_h5_layout_start_close || id == R.id.host_video_ad_app_layout_start_close) {
            if (mTipsAnimation.getVisibility() == View.VISIBLE) {
                cancelShowTipsAnimation();
            }
            // 提示条还未展示的情况下改变提示条的布局
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
            if (id == R.id.host_video_ad_h5_layout_start_close) {
                lp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 17));
            } else {
                lp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 62));
            }
            mTipsAnimation.setLayoutParams(lp);
            changeCard();
        } else {
            doClick(id == R.id.host_video_ad_video_bg_iv? 1 : 2);
        }
    }

    private void doClick(int clickAreaType) {
        if (mExtraParams != null && mExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK && AdUnlockUtil.needCalculateJumpDuration(mAdvertis)) {
            isJumping = true;
            VideoAdTrace.traceEvent(mAdvertis, "onClick_StartJumpCount");
            startJumpCountdown(mAdvertis.getActualStayTime());
            if (mAdvertis != null){
                mAdvertis.setDpRecordEnable(false);
            }
        } else if (!isAdClicked) {
            if (customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
                ((ICustomViewToActivityExt) customViewByCountDownStyle).onAdClicked();
            }
            if (mAdRewardCashTasksView != null) {
                mAdRewardCashTasksView.onAdClick();
            }
            updateButtonText(true);
            if (hasShowExtraClickButton) {
                updateExtraClickButtonText(true);
            }
            isAdClicked = true;
        }
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback->callback.onAdVideoClick(false, clickAreaType));
        if (mAdvertis.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT) {
            // 订阅类型，先订阅
            doCollect(mAdvertis.getRealLink());
        }
    }

    @SuppressLint("DefaultLocale")
    private void initConfig() {
        JSONObject config = ConfigureCenter.getInstance()
                .getJson(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_CLICK_CONFIG);
        if (config != null) {
            try {
                JSONObject tipsShowTimeConfig = config.optJSONObject("gestureShowTime");
                int positionId = mAdvertis.getPositionId();
                tipsShowTime = tipsShowTimeConfig.getInt(positionId + "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        clickButtonText = RewardVideoConfigManager.getInstance().getButtonText(
                RewardVideoConfigManager.getInstance().getRealConfigPositionName(mExtraParams),
                RewardVideoConfigManager.getInstance().getStyle(mExtraParams),
                mExtraParams != null ? mExtraParams.getRewardTime() : 0,
                AdUnlockUtil.needCalculateJumpDuration(mAdvertis) ? mAdvertis.getTipStayTime() : 0);
        if (tipsShowTime == 0) {
            tipsShowTime = 3;
        }

        extraClickTipsShowTime = 3;
        JSONObject clickDurationRewardConfig = ConfigureCenter.getInstance()
                .getJson(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_DURATION_AND_CLICK_CONFIG);
        if (clickDurationRewardConfig != null) {
            try {
                extraClickTipsShowTime = clickDurationRewardConfig.optInt("gestureShowTime");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (mExtraParams != null) {
            if (AdUnlockUtil.needCalculateJumpDuration(mAdvertis)) {
                if (mExtraParams.isShakeEnable()) {
                    extraClickButtonText = String.format("摇动/点击并浏览页面%d秒额外领取%d分钟",
                            mAdvertis.getTipStayTime(), mExtraParams.getExtraClickRewardTime());
                } else {
                    extraClickButtonText = String.format("点击并浏览页面%d秒额外领取%d分钟",
                            mAdvertis.getTipStayTime(), mExtraParams.getExtraClickRewardTime());
                }
            } else {
                if (mExtraParams.isShakeEnable()) {
                    extraClickButtonText = String.format("摇动/点击额外领取%d分钟时长", mExtraParams.getExtraClickRewardTime());
                } else {
                    extraClickButtonText = RewardVideoConfigManager.getInstance().getExtraRewardText(RewardVideoConfigManager.POSITION_NAME_INCENTIVE_DURATION_UNLOCK_TIME,
                            RewardVideoConfigManager.STYLE_DURATION, mExtraParams.getExtraClickRewardTime());
                }
            }
        }
    }

    public boolean isCashWithdrawal() {
        if (mExtraParams != null) {
            String positionName = mExtraParams.getPositionName();
            return positionName.equals(AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL);
        }
        return false;
    }

    private void updateButtonText(boolean isRewarded) {
        if (!canUpdateUi() || mExtraParams == null ||
                mExtraParams.getUnlockType() != AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK) {
            return;
        }
        if (isRewarded) {
            mLandStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mLandEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mAppStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mAppEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mNewBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            if (mAbstractThirdAd != null && !TextUtils.isEmpty(mAbstractThirdAd.getButtonText())){
                mLandStartBtn.setText(mAbstractThirdAd.getButtonText());
                mLandEndBtn.setText(mAbstractThirdAd.getButtonText());
                mAppStartBtn.setText(mAbstractThirdAd.getButtonText());
                mAppEndBtn.setText(mAbstractThirdAd.getButtonText());
                mNewBtn.setText(mAbstractThirdAd.getButtonText());
            }
            if (mExtraParams.isShakeEnable()) {
                hideShakeView();
            } else {
                cancelShowTipsAnimation();
                mTvVideoEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
                if (mAbstractThirdAd != null && !TextUtils.isEmpty(mAbstractThirdAd.getButtonText())) {
                    mTvVideoEndBtn.setText(mAbstractThirdAd.getButtonText());
                }
            }
            if (mExtraParams != null) {
                mExtraParams.setCanReward(true);
            }
            updateButtonWidth(false);
        } else {
            mLandStartBtn.setText(clickButtonText);
            mLandEndBtn.setText(clickButtonText);
            mAppStartBtn.setText(clickButtonText);
            mAppEndBtn.setText(clickButtonText);
            mNewBtn.setText(clickButtonText);
            if (mExtraParams.isShakeEnable()) {
                showShakeView();
            } else {
//                mTvVideoEndBtn.setText(clickButtonText);
                HandlerManager.postOnUIThreadDelay(showTipsRunnable, tipsShowTime * 1000);
            }
            updateButtonWidth(true);
        }
    }

    private void updateButtonWidth(boolean isLongWidth) {
        if (isLongWidth) {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mLandStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 12), lp.topMargin, BaseUtil.dp2px(getContext(), 12), lp.bottomMargin);
            mLandStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mLandEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mLandEndBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 12), lp.topMargin, BaseUtil.dp2px(getContext(), 12), lp.bottomMargin);
            mAppStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mAppEndBtn.setLayoutParams(lp);
        } else {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mLandStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mLandStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mLandEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 44), lp.topMargin, BaseUtil.dp2px(getContext(), 44), lp.bottomMargin);
            mLandEndBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mAppStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 44), lp.topMargin, BaseUtil.dp2px(getContext(), 44), lp.bottomMargin);
            mAppEndBtn.setLayoutParams(lp);
        }
    }

    private void updateExtraClickButtonText(boolean isClicked) {
        if (!canUpdateUi() || mExtraParams == null ||
                mExtraParams.getUnlockType() != AdUnlockUtil.REWARD_TYPE_DURATION_AND_CLICK) {
            return;
        }
        if (isClicked) {
            mLandStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mLandEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mAppStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mAppEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mNewBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            if (mExtraParams.isShakeEnable()) {
                hideShakeView();
            } else {
                cancelShowTipsAnimation();
            }
            mExtraParams.setExtraClickRewardSuccess(true);
            updateButtonWidth(false);
        } else {
            hasShowExtraClickButton = true;
            mLandStartBtn.setText(extraClickButtonText);
            mLandEndBtn.setText(extraClickButtonText);
            mAppStartBtn.setText(extraClickButtonText);
            mAppEndBtn.setText(extraClickButtonText);
            mNewBtn.setText(extraClickButtonText);
            if (mExtraParams.isShakeEnable()) {
                showShakeView();
            } else {
                HandlerManager.postOnUIThreadDelay(showTipsRunnable,extraClickTipsShowTime * 1000);
            }
            updateButtonWidth(true);
        }
    }

    //横版视频容器适配
    public boolean getHorContainerDisplayedEnable() {
        boolean enable = ConfigureCenter.getInstance().getBool("ad", "rewardVideoContainerAdapterEnable", true);
        return enable;
    }

    private void showShakeView() {
        if (mExtraParams.canReward()) {
            return;
        }
        if (mExtraParams.isExtraClickRewardSuccess()) {
            return;
        }
        if (AdManager.isDownloadAd(mAdvertis)) {
            mAppStartBtn.showShakeView();
            mAppEndBtn.showShakeView();
            mNewBtn.showShakeView();
        } else {
            mLandStartBtn.showShakeView();
            mLandEndBtn.showShakeView();
            mNewBtn.showShakeView();
        }
        startShakeDetect();
    }

    private void hideShakeView() {
        if (AdManager.isDownloadAd(mAdvertis)) {
            mAppStartBtn.hideShakeView();
            mAppEndBtn.hideShakeView();
            mNewBtn.hideShakeView();
        } else {
            mLandStartBtn.hideShakeView();
            mLandEndBtn.hideShakeView();
            mNewBtn.hideShakeView();
        }
        stopShakeDetect();
    }

    private void startShakeDetect() {
        if (mExtraParams == null || !mExtraParams.isShakeEnable()) {
            return;
        }
        if (mExtraParams.canReward()) {
            return;
        }
        if (mExtraParams.isExtraClickRewardSuccess()) {
            return;
        }
        hasShakeSuccess = false;
        if (isShakeDetecting) {
            return;
        }
        if (mShakeUtils == null) {
            int shakeSpeed = ConfigureCenter.getInstance()
                    .getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_INCENTIVE_SHAKE_SPEED, 15);
            if (mAdvertis != null && mAdvertis.getShakeSpeed() != 0) {
                shakeSpeed = mAdvertis.getShakeSpeed();
            }
            mShakeUtils = new ShakeUtilsNew(getContext(), shakeSpeed);
            mShakeUtils.setOnShakeListener(new ShakeUtilsNew.OnShakeListener() {
                @Override
                public void onShake() {
                    if (hasShakeSuccess) {
                        return;
                    }
                    hasShakeSuccess = true;
                    try {
                        Vibrator vibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
                        if (vibrator != null) {
                            vibrator.vibrate(500);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    doClick(1);
                }
            });
        }
        mShakeUtils.onResume();
        isShakeDetecting = true;
    }

    private void stopShakeDetect() {
        if (mExtraParams == null || !mExtraParams.isShakeEnable()) {
            return;
        }
        if (!isShakeDetecting) {
            return;
        }
        if (mShakeUtils != null) {
            mShakeUtils.onPause();
        }
        isShakeDetecting = false;
    }

    private void cancelShowTipsAnimation() {
        if (mExtraParams == null || mExtraParams.getUnlockType() != AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK) {
            return;
        }
        HandlerManager.removeCallbacks(showTipsRunnable);
        mTipsAnimation.cancelAnimation();
        mTipsAnimation.setVisibility(View.GONE);
    }

    private void changeCard() {
        View startView = AdManager.isDownloadAd(mAdvertis) ? mAppStartLayout : mLandStartLayout;
        View endView = AdManager.isDownloadAd(mAdvertis) ? mAppEndLayout : mLandEndLayout;
        endView.setAlpha(0f);
        endView.setVisibility(View.VISIBLE);
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                ObjectAnimator alphaAnimator1 = ObjectAnimator.ofFloat(startView,
                        "alpha", 1f, 0f);
                alphaAnimator1.setDuration(100);

                ObjectAnimator alphaAnimator2 = ObjectAnimator.ofFloat(endView,
                        "alpha", 0f, 1f);
                alphaAnimator1.setDuration(200);
                ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(endView,
                        "translationY", endView.getHeight(), 0);
                translationYAnimator.setDuration(200);
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(alphaAnimator1, alphaAnimator2, translationYAnimator);
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        startView.setVisibility(View.INVISIBLE);
                    }
                });
                animatorSet.start();
            }
        });
    }

    public void doCollect(String albumId) {
        AlbumM albumM = new AlbumM();
        albumM.setId(Long.parseLong(albumId));
        albumM.setFavorite(false);
        AlbumEventManage.doCollectActionV2(albumM, this, new ICollectStatusCallback() {
            @Override
            public void onCollectSuccess(int code, final boolean isCollected) {
                if (!canUpdateUi()) {
                    return;
                }
                if (isCollected) {
                    if (code == 0) {
//                        Toast toast = Toast.makeText(getContext(), "订阅成功~\r\n可在【我听】中查看", Toast.LENGTH_SHORT);
//                        TextView v = (TextView) toast.getView().findViewById(android.R.id.message);
//                        if (v != null) v.setGravity(Gravity.CENTER);
//                        toast.setGravity(Gravity.CENTER, 0, 0);
//                        toast.show();
                        ToastOption option = new ToastOption();
                        option.textGravity = Gravity.CENTER;
                        CustomToast.showToast("订阅成功~\r\n可在【我听】中查看",Toast.LENGTH_SHORT, option);
                    }
                    mLandStartBtn.setBackgroundResource(R.drawable.host_ad_action_btn_bg);
                    mLandStartBtn.setSelected(true);
                    mLandStartBtn.setText("立即查看");

                    mLandEndBtn.setBackgroundResource(R.drawable.host_ad_action_btn_bg);
                    mLandEndBtn.setSelected(true);
                    mLandEndBtn.setText("立即查看");

                    mNewBtn.setBackgroundResource(R.drawable.host_ad_action_btn_bg);
                    mNewBtn.setSelected(true);
                    mNewBtn.setText("立即查看");

                    mTvVideoEndBtn.setBackgroundResource(R.drawable.host_ad_action_btn_bg);
                    mTvVideoEndBtn.setSelected(true);
                    mTvVideoEndBtn.setText("立即查看");
                    mAdvertis.setClickType(1);
                    mAdvertis.setRealLink("iting://open?msg_type=13&album_id="+ albumId);
                }
            }

            @Override
            public void onError() {
            }
        });
    }

    private void initForClickDialog(BusinessExtraInfo.IncentivePopupInfo popupInfo) {
        if (popupInfo == null) {
            return;
        }
        // 在视频播放popupAfterSec秒后展示弹窗
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (canUpdateUi() && getActivity() != null && !getActivity().isFinishing() && isVisibleToUser && mAdvertis != null) {
                    showClickDialog(popupInfo, mAdvertis.getTipStayTime(), mAdvertis.getActualStayTime(), false);
                }
            }
        }, popupInfo.getPopupAfterSec() * 1000);
    }

    private void showClickDialog(BusinessExtraInfo.IncentivePopupInfo popupInfo, int tipStayTime, int actualStayTime, boolean isFromContinue) {
        try {
            VideoAdClickDialogFragment dialog = VideoAdClickDialogFragment.newInstance(
                    mAdvertis, popupInfo,
                    new VideoAdClickDialogFragment.IDialogCallback() {
                        @Override
                        public void onButtonClick() {
                            isClickDialogShowing = false;
                            if (mAdvertis == null) {
                                return;
                            }
                            isDialogClickJumping = true;
                            isJumping = true;
                            VideoAdTrace.traceEvent(mAdvertis, "onClick_StartJumpCount");
                            startJumpCountdown(actualStayTime);
                            if (mAdvertis != null){
                                mAdvertis.setDpRecordEnable(false);
                            }
                            reportDialogAction("dialogClick");
                            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback->callback.onAdVideoClick(false,6));
                        }

                        @Override
                        public void onClose(boolean isAuto) {
                            isClickDialogShowing = false;
                            // 弹窗关闭时恢复倒计时
                            if (mAdVideoPlayerManager != null) {
                                mAdVideoPlayerManager.onResumeMy();
                            }
                            if (mExtraParams != null && mExtraParams.getCountDownTimer() != null) {
                                mExtraParams.getCountDownTimer().resume();
                            }
                            if (mAdRewardCashTasksView != null && mAdRewardCashTasksView.getCountDownTimer() != null) {
                                mAdRewardCashTasksView.getCountDownTimer().resume();
                            }
                            reportDialogAction(isAuto ? "dialogAutoClose" : "dialogClose");
                        }
                    },
                    tipStayTime, isFromContinue
            );
            dialog.show(getChildFragmentManager(), "VideoAdClickDialog");
            reportDialogAction(isFromContinue ? "dialogShowAgain": "dialogShow");
            isClickDialogShowing = true;

            // 弹窗展示时暂停相关倒计时
            if (mAdVideoPlayerManager != null) {
                mAdVideoPlayerManager.onPauseMy();
            }
            if (mExtraParams != null && mExtraParams.getCountDownTimer() != null) {
                mExtraParams.getCountDownTimer().pause();
            }
            if (mAdRewardCashTasksView != null && mAdRewardCashTasksView.getCountDownTimer() != null) {
                mAdRewardCashTasksView.getCountDownTimer().pause();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void reportDialogAction(String action) {
        if (mAdvertis == null || mExtraParams == null) {
            return;
        }
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                mAdvertis, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_CLICK_DIALOG_ACTION, mExtraParams.getPositionName())
                        .sdkType(AdManager.getSDKType(mAdvertis) + "")
                        .uid(UserInfoMannage.getUid() + "")
                        .adUserType(mAdvertis.getAdUserType())
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(mExtraParams.getSourceName())
                                .action(action)
                        .showStyle(mAdvertis.getShowstyle() + "")
                        .build());
    }
}
